<template>
  <el-popover
    :visible="popoverVisible"
    placement="bottom-start"
    :width="200"
    trigger="manual"
    popper-class="status-selector-popover"
  >
    <template #reference>
      <div
        class="status-selector-trigger"
        :style="{ width: width }"
        @click="togglePopover"
        @mouseenter="isHovering = true"
        @mouseleave="isHovering = false"
      >
        <div class="status-display">
          <!-- 未选择状态 -->
          <span v-if="selectedStatusIds.length === 0" class="placeholder">
            {{ placeholder }}
          </span>
          <!-- 已选择状态 -->
          <div v-else class="selected-status-text">
            {{ getDisplayStatusText() }}
          </div>
        </div>
        <el-icon
          class="dropdown-icon"
          :class="{
            'is-reverse': popoverVisible && selectedStatusIds.length === 0,
            'is-clear': selectedStatusIds.length > 0 && isHovering,
          }"
          @click.stop="handleIconClick"
        >
          <CircleClose v-if="selectedStatusIds.length > 0 && isHovering" />
          <ArrowDown v-else />
        </el-icon>
      </div>
    </template>

    <!-- 弹出层内容 -->
    <div class="status-selector-content">
      <!-- 状态大类快速选择区域 -->
      <div class="status-categories-section">
        <!-- <div class="section-title">快速选择</div> -->
        <div class="status-category-buttons">
          <div class="category-row">
            <el-button
              v-for="category in statusCategories.slice(0, 2)"
              :key="category.value"
              :type="
                isStatusCategorySelected(category.value) ? 'primary' : 'default'
              "
              size="small"
              @click="handleStatusCategoryClick(category)"
              class="status-category-btn"
            >
              {{ category.label }}
            </el-button>
          </div>
          <div class="category-row">
            <el-button
              v-for="category in statusCategories.slice(2, 4)"
              :key="category.value"
              :type="
                isStatusCategorySelected(category.value) ? 'primary' : 'default'
              "
              size="small"
              @click="handleStatusCategoryClick(category)"
              class="status-category-btn"
            >
              {{ category.label }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <el-divider style="margin: 12px 0" />

      <!-- 精确选择区域 -->
      <div class="precise-selection-section">
        <!-- <div class="section-title">精确选择</div> -->
        <div class="tracker-hover-menu">
          <el-popover
            v-for="tracker in filteredTrackerTypeConfigs"
            :key="tracker.type"
            placement="right-start"
            :width="200"
            trigger="hover"
            :popper-class="`status-popover-${tracker.type}`"
          >
            <template #reference>
              <div class="tracker-menu-item">
                <div class="tracker-icon">
                  <CommonIcon
                    :issue-type="
                      getTrackerIcon({ tracker_type: tracker.value }, 'parent')
                    "
                    :size="16"
                  />
                </div>
                <div class="tracker-name">{{ tracker.label }}</div>
                <div
                  class="tracker-count"
                  v-if="getTrackerStatusCount(tracker.type) > 0"
                >
                  {{ getTrackerStatusCount(tracker.type) }}
                </div>
                <el-icon class="tracker-arrow">
                  <ArrowRight />
                </el-icon>
              </div>
            </template>

            <div class="status-popover-content">
              <!-- <div class="status-list-header">
                <span class="header-title">{{ tracker.label }}状态</span>
              </div> -->
              <div class="status-checkbox-list">
                <div
                  v-for="status in getTrackerStatuses(tracker.type)"
                  :key="status.id"
                  class="status-checkbox-item"
                  @click="toggleStatusSelection(tracker.type, status.id)"
                >
                  <el-checkbox
                    :model-value="
                      trackerSelectedStatuses[tracker.type]?.includes(status.id)
                    "
                    @change="
                      handleStatusToggle(tracker.type, status.id, $event)
                    "
                    class="status-checkbox"
                  />
                  <span class="status-name">{{ status.name }}</span>
                </div>
              </div>
              <div class="status-actions">
                <div class="action-left">
                  <el-checkbox
                    :model-value="isAllSelected(tracker.type)"
                    :indeterminate="isIndeterminate(tracker.type)"
                    @change="handleSelectAll(tracker.type, $event)"
                    class="select-all-checkbox"
                  >
                    全选 ({{ getTrackerStatusCount(tracker.type) }})
                  </el-checkbox>
                </div>
                <div class="action-right">
                  <el-button
                    size="small"
                    text
                    @click.stop="clearTrackerSelection(tracker.type)"
                    class="clear-btn"
                    :disabled="getTrackerStatusCount(tracker.type) === 0"
                  >
                    清空
                  </el-button>
                </div>
              </div>
            </div>
          </el-popover>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button size="small" @click="clearAll" class="clear-all-btn">
          清空
        </el-button>
        <!-- <el-button size="small" type="primary" @click="confirmSelection">
          确定
        </el-button> -->
      </div>
    </div>
  </el-popover>
</template>

<script setup>
  import { ref, watch, onMounted, onUnmounted, computed, nextTick } from 'vue'
  import { ArrowDown, ArrowRight, CircleClose } from '@element-plus/icons-vue'
  import { getTrackerIcon } from '@/utils/index'
  import CommonIcon from '@/components/CommonIcon.vue'
  import { getIssueStatus } from '@/api/projectIssue'
  import { getProjectStatuses as getCustomWorkflowStatuses } from '@/api/customWorkflow'

  const props = defineProps({
    // 基础配置
    modelValue: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请选择状态',
    },
    width: {
      type: String,
      default: '150px',
    },

    // 数据源配置
    projectId: {
      type: [Number, String],
      default: null,
    },
    trackerType: {
      type: String,
      default: null,
    },

    // 存储配置
    storageKey: {
      type: String,
      default: null,
    },

    // 显示配置
    showTrackerTypes: {
      type: Array,
      default: () => ['requirement', 'bug', 'task'],
    },
  })

  const emit = defineEmits(['update:modelValue', 'change'])

  // 响应式数据
  const loading = ref(false)
  const selectedStatusIds = ref([])
  const rawStatusList = ref([])
  const popoverVisible = ref(false) // 弹出层显示状态
  const isHovering = ref(false) // 触发器悬停状态
  const quickSelectedStatuses = ref([]) // 快速选择的状态ID（独立）
  const trackerSelectedStatuses = ref({
    requirement: [],
    task: [],
    bug: [],
  }) // 各事项类型选中的状态（精确选择）

  // 事项类型配置
  const trackerTypeConfigs = [
    { type: 'requirement', label: '需求', value: 'requirement' },
    { type: 'task', label: '任务', value: 'task' },
    { type: 'bug', label: '缺陷', value: 'bug' },
  ]

  // 过滤后的事项类型配置（按照showTrackerTypes的顺序排序）
  const filteredTrackerTypeConfigs = computed(() => {
    const filtered = props.showTrackerTypes
      .map((type) =>
        trackerTypeConfigs.find((tracker) => tracker.type === type)
      )
      .filter(Boolean) // 过滤掉undefined的项
    return filtered
  })

  // 状态大类配置
  const statusCategories = [
    { value: 'not_started', label: '未开始', category: 'not_started' },
    { value: 'in_progress', label: '进行中', category: 'in_progress' },
    {
      value: 'awaiting_acceptance',
      label: '待验收',
      category: 'awaiting_acceptance',
    },
    { value: 'completed', label: '已完成', category: 'completed' },
  ]

  /**
   * 获取状态数据
   */
  const fetchStatusData = async () => {
    loading.value = true
    try {
      let data = []

      if (props.trackerType) {
        const result = await getCustomWorkflowStatuses({
          tracker_type: props.trackerType,
        })
        data = result.data || []
      } else if (props.projectId) {
        try {
          const result = await getCustomWorkflowStatuses({
            project_id: props.projectId,
          })
          data = result.data || []
        } catch (error) {
          const result = await getIssueStatus()
          data = result.data || []
        }
      } else {
        const result = await getIssueStatus()
        data = result.data || []
      }

      rawStatusList.value = data.map((item) => ({
        id: item.id,
        name: item.name,
        status_category: item.status_category,
        tracker_type: item.tracker_type,
        is_closed: item.is_closed,
        position: item.position,
      }))
    } catch (error) {
      console.error('获取状态数据失败:', error)
      rawStatusList.value = []
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换弹出层显示状态
   */
  const togglePopover = () => {
    popoverVisible.value = !popoverVisible.value
  }

  /**
   * 获取指定事项类型的状态列表
   */
  const getTrackerStatuses = (trackerType) => {
    return rawStatusList.value.filter((status) => {
      return status.tracker_type === trackerType || status.tracker_type === null
    })
  }

  /**
   * 判断状态大类是否被选中（基于快速选择）
   */
  const isStatusCategorySelected = (categoryValue) => {
    const categoryStatuses = rawStatusList.value.filter((status) => {
      // 根据showTrackerTypes动态过滤tracker类型
      if (!props.showTrackerTypes.includes(status.tracker_type)) return false

      return (
        status.status_category === categoryValue ||
        (status.status_category === null &&
          getDefaultCategoryForStatus(status.id) === categoryValue)
      )
    })

    const categoryStatusIds = categoryStatuses.map((s) => s.id)
    return categoryStatusIds.every((id) =>
      quickSelectedStatuses.value.includes(id)
    )
  }

  /**
   * 处理状态大类点击
   */
  const handleStatusCategoryClick = (category) => {
    const categoryStatuses = rawStatusList.value.filter((status) => {
      // 根据showTrackerTypes动态过滤tracker类型
      if (!props.showTrackerTypes.includes(status.tracker_type)) return false

      return (
        status.status_category === category.category ||
        (status.status_category === null &&
          getDefaultCategoryForStatus(status.id) === category.category)
      )
    })

    const categoryStatusIds = categoryStatuses.map((s) => s.id)
    const isSelected = categoryStatusIds.every((id) =>
      quickSelectedStatuses.value.includes(id)
    )

    if (isSelected) {
      // 取消选择该大类（仅从快速选择中移除）
      quickSelectedStatuses.value = quickSelectedStatuses.value.filter(
        (id) => !categoryStatusIds.includes(id)
      )
    } else {
      // 选择该大类（仅添加到快速选择中）
      categoryStatusIds.forEach((statusId) => {
        if (!quickSelectedStatuses.value.includes(statusId)) {
          quickSelectedStatuses.value.push(statusId)
        }
      })
    }

    updateSelectedStatusIds()
  }

  /**
   * 获取事项类型选中状态数量
   */
  const getTrackerStatusCount = (trackerType) => {
    return trackerSelectedStatuses.value[trackerType]?.length || 0
  }

  /**
   * 清空事项类型选择
   */
  const clearTrackerSelection = (trackerType) => {
    trackerSelectedStatuses.value[trackerType] = []
    updateSelectedStatusIds()
  }

  /**
   * 全选事项类型状态
   */
  const selectAllTrackerStatuses = (trackerType) => {
    const allStatuses = getTrackerStatuses(trackerType)
    trackerSelectedStatuses.value[trackerType] = allStatuses.map(
      (status) => status.id
    )
    updateSelectedStatusIds()
  }

  /**
   * 判断是否全选
   */
  const isAllSelected = (trackerType) => {
    const allStatuses = getTrackerStatuses(trackerType)
    const selectedStatuses = trackerSelectedStatuses.value[trackerType] || []
    return (
      allStatuses.length > 0 && selectedStatuses.length === allStatuses.length
    )
  }

  /**
   * 判断是否半选状态
   */
  const isIndeterminate = (trackerType) => {
    const allStatuses = getTrackerStatuses(trackerType)
    const selectedStatuses = trackerSelectedStatuses.value[trackerType] || []
    return (
      selectedStatuses.length > 0 &&
      selectedStatuses.length < allStatuses.length
    )
  }

  /**
   * 处理全选checkbox变化
   */
  const handleSelectAll = (trackerType, checked) => {
    if (checked) {
      selectAllTrackerStatuses(trackerType)
    } else {
      clearTrackerSelection(trackerType)
    }
  }

  /**
   * 切换状态选择
   */
  const toggleStatusSelection = (trackerType, statusId) => {
    const currentSelection = [...trackerSelectedStatuses.value[trackerType]]
    const index = currentSelection.indexOf(statusId)

    if (index > -1) {
      currentSelection.splice(index, 1)
    } else {
      currentSelection.push(statusId)
    }

    trackerSelectedStatuses.value[trackerType] = currentSelection
    updateSelectedStatusIds()
  }

  /**
   * 处理状态切换
   */
  const handleStatusToggle = (trackerType, statusId, checked) => {
    const currentSelection = [...trackerSelectedStatuses.value[trackerType]]

    if (checked) {
      if (!currentSelection.includes(statusId)) {
        currentSelection.push(statusId)
      }
    } else {
      const index = currentSelection.indexOf(statusId)
      if (index > -1) {
        currentSelection.splice(index, 1)
      }
    }

    trackerSelectedStatuses.value[trackerType] = currentSelection
    updateSelectedStatusIds()
  }

  /**
   * 更新最终的选中状态ID列表（合并快速选择和精确选择）
   */
  const updateSelectedStatusIds = () => {
    const allSelected = []

    // 收集快速选择的状态
    allSelected.push(...quickSelectedStatuses.value)

    // 收集各事项类型精确选择的状态
    Object.values(trackerSelectedStatuses.value).forEach((statusIds) => {
      allSelected.push(...statusIds)
    })

    selectedStatusIds.value = [...new Set(allSelected)] // 去重

    emit('update:modelValue', selectedStatusIds.value)
    emit('change', selectedStatusIds.value)

    // 分别保存快速选择和精确选择到localStorage
    if (props.storageKey) {
      // 保存快速选择的状态
      const quickValueToSave =
        quickSelectedStatuses.value.length > 0
          ? quickSelectedStatuses.value.join(',')
          : ''
      localStorage.setItem(`${props.storageKey}_quick`, quickValueToSave)

      // 保存精确选择的状态
      const trackerValueToSave = JSON.stringify(trackerSelectedStatuses.value)
      localStorage.setItem(`${props.storageKey}_tracker`, trackerValueToSave)
    }
  }

  /**
   * 获取显示的状态文本（用|分隔）
   */
  const getDisplayStatusText = () => {
    const statusNames = selectedStatusIds.value
      .map((id) => rawStatusList.value.find((status) => status.id === id))
      .filter(Boolean)
      .map((status) => status.name)

    const text = statusNames.join(' | ')

    // 如果文本过长，进行截断
    if (text.length > 50) {
      return text.substring(0, 47) + '...'
    }

    return text
  }

  /**
   * 清空所有选择
   */
  const clearAll = () => {
    selectedStatusIds.value = []
    quickSelectedStatuses.value = []
    trackerSelectedStatuses.value = {
      requirement: [],
      task: [],
      bug: [],
    }
    updateSelectedStatusIds()
  }

  /**
   * 处理图标点击事件
   */
  const handleIconClick = () => {
    if (selectedStatusIds.value.length > 0) {
      // 有选中状态时，点击清除图标清空所有选择
      clearAll()
    } else {
      // 没有选中状态时，点击箭头图标切换弹出层显示
      popoverVisible.value = !popoverVisible.value
    }
  }

  /**
   * 确认选择并关闭弹出层
   */
  const confirmSelection = () => {
    popoverVisible.value = false
  }

  /**
   * 兜底逻辑：为没有 status_category 的状态提供默认分类
   */
  const getDefaultCategoryForStatus = (statusId) => {
    const notStartedIds = [1, 11]
    const inProgressIds = [2, 7, 6, 9, 10, 14, 12]
    const completedIds = [3, 4, 5, 13]

    if (notStartedIds.includes(statusId)) return 'not_started'
    if (inProgressIds.includes(statusId)) return 'in_progress'
    if (completedIds.includes(statusId)) return 'completed'

    return 'in_progress' // 默认归类到进行中
  }

  /**
   * 从localStorage恢复状态
   */
  const restoreFromStorage = () => {
    if (props.storageKey) {
      // 清空现有状态
      quickSelectedStatuses.value = []
      trackerSelectedStatuses.value = {
        requirement: [],
        task: [],
        bug: [],
      }

      // 恢复快速选择的状态
      const savedQuickValue = localStorage.getItem(`${props.storageKey}_quick`)
      if (savedQuickValue) {
        const quickStatusIds = savedQuickValue
          .split(',')
          .map((id) => parseInt(id))
        quickSelectedStatuses.value = quickStatusIds
      }

      // 恢复精确选择的状态
      const savedTrackerValue = localStorage.getItem(
        `${props.storageKey}_tracker`
      )
      if (savedTrackerValue) {
        try {
          const trackerData = JSON.parse(savedTrackerValue)
          trackerSelectedStatuses.value = {
            requirement: trackerData.requirement || [],
            task: trackerData.task || [],
            bug: trackerData.bug || [],
          }
        } catch (error) {
          console.error('解析精确选择状态失败:', error)
        }
      }

      // 兼容旧的存储格式
      if (!savedQuickValue && !savedTrackerValue) {
        const oldSavedValue = localStorage.getItem(props.storageKey)
        if (oldSavedValue) {
          console.log('检测到旧的存储格式，正在迁移...')
          const statusIds = oldSavedValue.split(',').map((id) => parseInt(id))

          // 智能分配到快速选择和精确选择
          statusIds.forEach((statusId) => {
            const status = rawStatusList.value.find((s) => s.id === statusId)
            if (
              status &&
              props.showTrackerTypes.includes(status.tracker_type)
            ) {
              // 检查是否属于状态大类
              const belongsToCategory = statusCategories.find((category) => {
                const categoryStatuses = rawStatusList.value.filter((s) => {
                  // 根据showTrackerTypes动态过滤tracker类型
                  if (!props.showTrackerTypes.includes(s.tracker_type))
                    return false
                  return (
                    s.status_category === category.category ||
                    (s.status_category === null &&
                      getDefaultCategoryForStatus(s.id) === category.category)
                  )
                })
                return categoryStatuses.map((s) => s.id).includes(statusId)
              })

              if (belongsToCategory) {
                quickSelectedStatuses.value.push(statusId)
              } else {
                const trackerType = status.tracker_type || 'requirement'
                if (props.showTrackerTypes.includes(trackerType)) {
                  trackerSelectedStatuses.value[trackerType].push(statusId)
                }
              }
            }
          })

          // 保存为新格式并删除旧格式
          updateSelectedStatusIds()
          localStorage.removeItem(props.storageKey)
        }
      }

      // 更新最终的选中状态列表
      const allSelected = []
      allSelected.push(...quickSelectedStatuses.value)
      Object.values(trackerSelectedStatuses.value).forEach((statusIds) => {
        allSelected.push(...statusIds)
      })
      selectedStatusIds.value = [...new Set(allSelected)]

      // 恢复完成后触发change事件，通知父组件进行数据筛选
      if (selectedStatusIds.value.length > 0) {
        nextTick(() => {
          emit('update:modelValue', selectedStatusIds.value)
          emit('change', selectedStatusIds.value)
        })
      }
    }
  }

  // 监听props变化
  watch(
    () => [props.projectId, props.trackerType],
    () => {
      fetchStatusData()
    },
    { deep: true }
  )

  watch(
    () => props.modelValue,
    (newValue) => {
      selectedStatusIds.value = newValue || []
    },
    { immediate: true }
  )

  /**
   * 处理点击外部区域关闭弹出层
   */
  const handleClickOutside = (event) => {
    if (!popoverVisible.value) return

    // 检查点击的元素是否在弹出层内部
    const popoverElement = document.querySelector('.status-selector-popover')
    const triggerElement = event.target.closest('.status-selector-trigger')
    const statusPopoverElement = event.target.closest(
      '[class*="status-popover-"]'
    )

    // 如果点击的是触发器、主弹出层或状态弹出框，则不关闭
    if (
      triggerElement ||
      (popoverElement && popoverElement.contains(event.target)) ||
      statusPopoverElement
    ) {
      return
    }

    popoverVisible.value = false
  }

  // 组件挂载
  onMounted(async () => {
    await fetchStatusData()
    // 使用nextTick确保父组件已经完全初始化
    await nextTick()
    restoreFromStorage()
    // 添加全局点击监听器
    document.addEventListener('click', handleClickOutside)
  })

  // 组件卸载
  onUnmounted(() => {
    // 移除全局点击监听器
    document.removeEventListener('click', handleClickOutside)
  })

  // 暴露方法
  defineExpose({
    refresh: fetchStatusData,
    getSelectedStatuses: () => selectedStatusIds.value,
    getRawStatusList: () => rawStatusList.value,
  })
</script>

<style lang="scss" scoped>
  // 触发器样式
  .status-selector-trigger {
    box-shadow: 0 0 0 0 #dcdfe6, inset 0 0 0 1px #dcdfe6;
    border-radius: 6px;
    background-color: #fff;
    padding: 0 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    transition: border-color 0.2s;

    &:hover {
      border-color: #c0c4cc;
    }

    .status-display {
      flex: 1;
      overflow: hidden;

      .placeholder {
        color: #a9acb3;
        font-size: 14px;
        font-weight: 500;
      }

      .selected-status-text {
        color: #606266;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .dropdown-icon {
      margin-left: 8px;
      transition: transform 0.2s, color 0.2s;
      color: #c0c4cc;
      cursor: pointer;

      &.is-reverse {
        transform: rotate(180deg);
      }

      &.is-clear {
        color: #909399;
      }
    }
  }

  // 弹出层内容样式
  .status-selector-content {
    padding: 12px;

    .section-title {
      font-size: 12px;
      color: #909399;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .status-categories-section {
      margin-bottom: 12px;

      .status-category-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .category-row {
          display: flex;
          gap: 8px;

          .status-category-btn {
            flex: 1;
            min-width: 80px;
          }
        }
      }
    }

    .precise-selection-section {
      margin-bottom: 12px;

      .tracker-hover-menu {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        overflow: hidden;
        background: #fff;

        .tracker-menu-item {
          display: flex;
          align-items: center;
          padding: 10px 12px;
          cursor: pointer;
          transition: background-color 0.2s ease;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: #f5f7fa;
          }

          .tracker-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #909399;

            i {
              font-size: 12px;
            }
          }

          .tracker-name {
            flex: 1;
            font-size: 13px;
            color: #303133;
            font-weight: 500;
          }

          .tracker-count {
            background-color: #409eff;
            color: #fff;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: 6px;
            min-width: 16px;
            text-align: center;
            font-weight: 500;
          }

          .tracker-arrow {
            color: #c0c4cc;
            font-size: 10px;
          }
        }
      }

      // 弹出框内容样式
      .status-popover-content {
        display: flex;
        flex-direction: column;
        max-height: 240px;

        .status-list-header {
          flex-shrink: 0;
          padding: 8px 12px 0px 12px;
          border-bottom: 1px solid #f0f0f0;
          background-color: #fafafa;

          .header-title {
            font-size: 12px;
            font-weight: 500;
            color: #303133;
          }
        }

        .status-checkbox-list {
          flex: 1;
          overflow-y: auto;
          padding: 8px 0;

          .status-checkbox-item {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;

            // &:hover {
            //   background-color: #f5f7fa;
            // }

            .status-checkbox {
              margin-right: 0;
              flex-shrink: 0;

              :deep(.el-checkbox) {
                margin-right: 8px;
              }

              :deep(.el-checkbox__label) {
                padding-left: 0;
                margin-left: 0;
              }
            }

            .status-name {
              flex: 1;
              font-size: 12px;
              color: #606266;
            }
          }

          // 自定义滚动条
          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: #f5f7fa;
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c0c4cc;
            border-radius: 2px;

            // &:hover {
            //   background: #909399;
            // }
          }
        }

        .status-actions {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 12px;
          margin: 0 -12px -12px -12px;
          border-top: 1px solid #e4e7ed;
          background-color: #fafafa;
          width: calc(100% + 24px);

          .action-left {
            .select-all-checkbox {
              font-size: 13px;
              color: #606266;

              :deep(.el-checkbox__label) {
                font-size: 13px;
                color: #606266;
              }
            }
          }

          .action-right {
            .clear-btn {
              font-size: 13px;
              color: #909399;
              padding: 0;
              height: auto;

              &:disabled {
                color: #c0c4cc;
                cursor: not-allowed;
              }
            }
          }
        }
      }
    }

    // 自定义选项样式
    .custom-option {
      display: flex;
      align-items: center;
      gap: 4px;
      width: 100%;

      .option-checkbox {
        margin: 0;
      }

      .option-label {
        flex: 1;
        font-size: 12px;
      }
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;

      .clear-all-btn {
        min-width: 80px;
      }
    }
  }
</style>

<style>
  /* 全局样式：弹出层样式 */
  .status-selector-popover {
    padding: 0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e4e7ed !important;
  }

  .status-popover-content
    .status-checkbox-list
    .status-checkbox-item
    .el-checkbox {
    margin-right: 8px !important;
  }

  .status-popover-content
    .status-checkbox-list
    .status-checkbox-item
    :deep(.el-checkbox__label) {
    padding-left: 0 !important;
    margin-left: 0 !important;
  }

  /* 状态弹出框样式 */
  [class*='status-popover-'] {
    .el-popover__content {
      padding: 0 !important;
      border-radius: 6px !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
      border: 1px solid #e4e7ed !important;
      overflow: hidden !important;
    }
  }

  /* 状态弹出框内容样式 */
  .status-popover-content {
    .status-actions {
      border-top: 1px solid #e4e7ed !important;
      background-color: #fafafa !important;
      padding: 8px 12px !important;
      margin: 0 -12px -12px -12px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;
      width: calc(100% + 24px) !important;
    }
  }

  /* 确保弹出框内容无底部空白 */
  .status-popover-content {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
  }
</style>
