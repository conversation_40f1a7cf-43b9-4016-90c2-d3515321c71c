/**
 * FMS 权限校验组合式函数
 */

import { computed, ref } from 'vue'
import { usePermissionStore } from '@/views/fms/stores/permissionStore'
import { useUserStore } from '@/store/modules/user'

// 权限位常量
export const FMS_PERMISSIONS = {
  VIEW: 1,
  UPLOAD: 2,
  DELETE: 4,
  SHARE: 8,
  MANAGE: 16,
  ADMIN: 32,
} as const

export type FmsPermissionBit =
  (typeof FMS_PERMISSIONS)[keyof typeof FMS_PERMISSIONS]

/**
 * FMS 权限组合式函数
 */
export function useFmsPermission() {
  const permissionStore = usePermissionStore()
  const userStore = useUserStore()

  /**
   * 检查权限
   * @param targetType 目标类型
   * @param targetId 目标ID
   * @param permissionBit 权限位
   * @returns 是否有权限
   */
  const can = (
    targetType: 'directory' | 'file',
    targetId: number,
    permissionBit: FmsPermissionBit
  ): boolean => {
    const key = `${targetType}:${targetId}`
    const permissions = (
      permissionStore.targetPermissions as Record<string, number>
    )[key]

    if (permissions === undefined) {
      return false
    }

    return (permissions & permissionBit) === permissionBit
  }

  /**
   * 检查是否有任意一个权限
   * @param targetType 目标类型
   * @param targetId 目标ID
   * @param permissionBits 权限位数组
   * @returns 是否有任意权限
   */
  const canAny = (
    targetType: 'directory' | 'file',
    targetId: number,
    permissionBits: FmsPermissionBit[]
  ): boolean => {
    return permissionBits.some((bit) => can(targetType, targetId, bit))
  }

  /**
   * 检查是否有所有权限
   * @param targetType 目标类型
   * @param targetId 目标ID
   * @param permissionBits 权限位数组
   * @returns 是否有所有权限
   */
  const canAll = (
    targetType: 'directory' | 'file',
    targetId: number,
    permissionBits: FmsPermissionBit[]
  ): boolean => {
    return permissionBits.every((bit) => can(targetType, targetId, bit))
  }

  /**
   * 获取有效权限
   * @param targetType 目标类型
   * @param targetId 目标ID
   * @returns 有效权限位
   */
  const getEffectivePermissions = (
    targetType: 'directory' | 'file',
    targetId: number
  ): number => {
    const key = `${targetType}:${targetId}`
    return (
      (permissionStore.targetPermissions as Record<string, number>)[key] || 0
    )
  }

  /**
   * 加载目标权限
   * @param targetType 目标类型
   * @param targetId 目标ID
   * @param userId 用户ID（可选，默认当前用户）
   */
  const loadPermissions = async (
    targetType: 'directory' | 'file',
    targetId: number,
    userId?: number
  ) => {
    const currentUserId = userId || (userStore as any).user_id
    if (!currentUserId) return

    try {
      await permissionStore.computeEffectivePermissions(
        currentUserId,
        targetType,
        targetId
      )
    } catch (error) {
      console.error('加载权限失败:', error)
    }
  }

  /**
   * 创建权限计算属性
   * @param targetType 目标类型
   * @param targetId 目标ID
   * @returns 权限计算属性对象
   */
  const createPermissionComputed = (
    targetType: 'directory' | 'file',
    targetId: number
  ) => {
    return {
      canView: computed(() => can(targetType, targetId, FMS_PERMISSIONS.VIEW)),
      canUpload: computed(() =>
        can(targetType, targetId, FMS_PERMISSIONS.UPLOAD)
      ),
      canDelete: computed(() =>
        can(targetType, targetId, FMS_PERMISSIONS.DELETE)
      ),
      canShare: computed(() =>
        can(targetType, targetId, FMS_PERMISSIONS.SHARE)
      ),
      canManage: computed(() =>
        can(targetType, targetId, FMS_PERMISSIONS.MANAGE)
      ),
      isAdmin: computed(() => can(targetType, targetId, FMS_PERMISSIONS.ADMIN)),
      effectivePermissions: computed(() =>
        getEffectivePermissions(targetType, targetId)
      ),
    }
  }

  /**
   * 权限标签映射
   */
  const permissionLabels = {
    [FMS_PERMISSIONS.VIEW]: '查看',
    [FMS_PERMISSIONS.UPLOAD]: '上传',
    [FMS_PERMISSIONS.DELETE]: '删除',
    [FMS_PERMISSIONS.SHARE]: '分享',
    [FMS_PERMISSIONS.MANAGE]: '管理',
    [FMS_PERMISSIONS.ADMIN]: '管理员',
  }

  /**
   * 获取权限标签
   * @param permissionBits 权限位
   * @returns 权限标签数组
   */
  const getPermissionLabels = (permissionBits: number): string[] => {
    const labels: string[] = []

    Object.entries(FMS_PERMISSIONS).forEach(([key, bit]) => {
      if ((permissionBits & bit) === bit) {
        labels.push(permissionLabels[bit])
      }
    })

    return labels
  }

  /**
   * 权限位操作工具
   */
  const permissionUtils = {
    /**
     * 添加权限位
     */
    add: (permissions: number, bit: FmsPermissionBit): number =>
      permissions | bit,

    /**
     * 移除权限位
     */
    remove: (permissions: number, bit: FmsPermissionBit): number =>
      permissions & ~bit,

    /**
     * 切换权限位
     */
    toggle: (permissions: number, bit: FmsPermissionBit): number =>
      permissions ^ bit,

    /**
     * 检查是否包含权限位
     */
    has: (permissions: number, bit: FmsPermissionBit): boolean =>
      (permissions & bit) === bit,

    /**
     * 合并权限位
     */
    merge: (...permissions: number[]): number =>
      permissions.reduce((acc, perm) => acc | perm, 0),

    /**
     * 获取权限位差异
     */
    diff: (
      oldPermissions: number,
      newPermissions: number
    ): { added: number; removed: number } => ({
      added: newPermissions & ~oldPermissions,
      removed: oldPermissions & ~newPermissions,
    }),
  }

  return {
    // 权限常量
    FMS_PERMISSIONS,

    // 权限检查方法
    can,
    canAny,
    canAll,
    getEffectivePermissions,
    loadPermissions,
    createPermissionComputed,

    // 权限标签
    permissionLabels,
    getPermissionLabels,

    // 权限工具
    permissionUtils,

    // Store 引用
    permissionStore,
  }
}

/**
 * 权限指令参数接口
 */
export interface PermissionDirectiveBinding {
  targetType: 'directory' | 'file'
  targetId: number
  permission: FmsPermissionBit | FmsPermissionBit[]
  mode?: 'any' | 'all' // 多权限时的检查模式
  fallback?: 'hide' | 'disable' // 无权限时的处理方式
}

/**
 * 权限指令实现
 */
export const vFmsPermission = {
  mounted(el: HTMLElement, binding: { value: PermissionDirectiveBinding }) {
    const {
      targetType,
      targetId,
      permission,
      mode = 'any',
      fallback = 'hide',
    } = binding.value
    const { can, canAny, canAll } = useFmsPermission()

    let hasPermission = false

    if (Array.isArray(permission)) {
      hasPermission =
        mode === 'all'
          ? canAll(targetType, targetId, permission)
          : canAny(targetType, targetId, permission)
    } else {
      hasPermission = can(targetType, targetId, permission)
    }

    if (!hasPermission) {
      if (fallback === 'hide') {
        el.style.display = 'none'
      } else if (fallback === 'disable') {
        el.setAttribute('disabled', 'true')
        el.style.opacity = '0.5'
        el.style.cursor = 'not-allowed'
      }
    }
  },

  updated(el: HTMLElement, binding: { value: PermissionDirectiveBinding }) {
    // 权限更新时重新检查
    this.mounted(el, binding)
  },
}
