<template>
  <div class="fms-main">
    <el-container class="fms-main__container">
      <el-header class="fms-main__header">
        <Breadcrumb
          :items="breadcrumbItems"
          :can-go-back="navigationState.canGoBack"
          :can-go-forward="navigationState.canGoForward"
          @change="onBreadcrumbChange"
          @navigate="handleBreadcrumbNavigation"
        />
        <div class="fms-main__toolbar">
          <div class="fms-main__search">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件和目录"
              :prefix-icon="Search"
              @keyup.enter="onSearch"
              clearable
            />
          </div>
        </div>
      </el-header>

      <el-container class="fms-main__body">
        <el-aside class="fms-main__aside" width="280px">
          <DirectoryTree
            :selected-id="currentDirectoryId"
            :height="treeHeight"
            @select="onDirectorySelect"
          />
        </el-aside>

        <el-main class="fms-main__main">
          <!-- 统一文件和目录列表 -->
          <UnifiedFileList
            :directory-id="currentDirectoryId"
            :search-keyword="searchKeyword"
            @refresh="refreshData"
            @directory-enter="onDirectoryEnter"
            @file-preview="onFilePreview"
            @item-rename="onItemRename"
            @item-delete="onItemDelete"
            @item-move="onItemMove"
            @upload="handleUpload"
          />
        </el-main>
      </el-container>
    </el-container>

    <!-- 对话框组件 -->
    <CreateDirectoryDialog
      v-model:visible="dialogs.createDirectory"
      :parent-id="currentDirectoryId"
      @success="onDirectoryCreated"
    />

    <RenameDirectoryDialog
      v-model:visible="dialogs.renameDirectory"
      :id="selectedDirectory?.id"
      :name="selectedDirectory?.name"
      @success="onDirectoryRenamed"
    />

    <MoveDirectoryDialog
      v-model:visible="dialogs.moveDirectory"
      :id="selectedDirectory?.id"
      :target-parent-id="selectedDirectory?.parentId"
      @success="onDirectoryMoved"
    />

    <RenameDialog
      v-model="dialogs.rename"
      :item="selectedItem"
      @success="onItemRenamed"
    />

    <MoveDialog
      v-model="dialogs.move"
      :item="selectedItem"
      @success="onItemMoved"
    />

    <FileUpload
      v-model="dialogs.fileUpload"
      :directory-id="currentDirectoryId"
      @success="onFileUploaded"
    />

    <FilePreview
      v-if="previewFile"
      :file-id="previewFile.id"
      @close="closeFilePreview"
    />
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, inject } from 'vue'
  import { useFmsStore } from './stores/fmsStore'
  import { useDirectoryStore } from './stores/directoryStore'
  import { useFileStore } from './stores/fileStore'

  // Element Plus 图标
  import { Upload, Search } from '@element-plus/icons-vue'

  // 通用组件
  import Breadcrumb from './components/Breadcrumb.vue'

  // 目录组件
  import DirectoryTree from './components/directory/DirectoryTree.vue'
  import CreateDirectoryDialog from './components/directory/CreateDirectoryDialog.vue'
  import RenameDirectoryDialog from './components/directory/RenameDirectoryDialog.vue'
  import MoveDirectoryDialog from './components/directory/MoveDirectoryDialog.vue'

  // 文件组件
  import UnifiedFileList from './components/UnifiedFileList.vue'
  import FilePreview from './components/file/FilePreview.vue'
  import FileUpload from './components/FileUpload.vue'
  import RenameDialog from './components/RenameDialog.vue'
  import MoveDialog from './components/MoveDialog.vue'

  // 注入基础服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // 响应式数据
  const searchKeyword = ref('')
  const currentDirectoryId = ref(null)
  const selectedDirectory = ref(null)
  const selectedItem = ref(null)
  const selectedFile = ref(null)
  const previewFile = ref(null)
  const treeHeight = ref('calc(100vh - 200px)')

  // 导航历史记录管理
  const navigationHistory = ref([])
  const currentHistoryIndex = ref(-1)

  // 对话框状态
  const dialogs = reactive({
    createDirectory: false,
    renameDirectory: false,
    moveDirectory: false,
    rename: false,
    move: false,
    fileUpload: false,
    filePreview: false,
  })

  // 面包屑路径数据
  const breadcrumbItems = ref([{ name: '根目录', id: null }])

  // 更新面包屑路径
  const updateBreadcrumb = async () => {
    const items = [{ name: '根目录', id: null }]

    if (currentDirectoryId.value) {
      const directoryStore = useDirectoryStore()

      try {
        // 使用异步方法获取完整路径
        const pathItems = await directoryStore.getDirectoryPath(
          currentDirectoryId.value
        )

        if (pathItems && pathItems.length > 0) {
          items.push(...pathItems)
        } else {
          // 如果无法获取完整路径，至少显示当前目录
          items.push({ name: '当前目录', id: currentDirectoryId.value })
        }
      } catch (error) {
        console.error('Failed to get directory path:', error)
        // 出错时也显示当前目录
        items.push({ name: '当前目录', id: currentDirectoryId.value })
      }
    }

    breadcrumbItems.value = items
  }

  // 导航状态计算属性
  const navigationState = computed(() => {
    return {
      canGoBack: currentHistoryIndex.value > 0,
      canGoForward:
        currentHistoryIndex.value < navigationHistory.value.length - 1,
    }
  })

  // 方法
  const onBreadcrumbChange = async (item) => {
    currentDirectoryId.value = item.id
    addToHistory(item.id)
    await updateBreadcrumb()
    refreshData()
  }

  const onSearch = () => {
    refreshData()
  }

  const onDirectorySelect = async (node) => {
    currentDirectoryId.value = node.id
    addToHistory(node.id)
    await updateBreadcrumb()
    refreshData()
  }

  const refreshData = () => {
    // 统一文件列表组件会自动刷新数据
  }

  // 工具栏操作
  const handleUpload = () => {
    dialogs.fileUpload = true
  }

  const handleRefresh = () => {
    refreshData()
  }

  // 统一文件列表事件处理
  const onDirectoryEnter = async (directory) => {
    currentDirectoryId.value = directory.id
    addToHistory(directory.id)
    await updateBreadcrumb()
  }

  const onFilePreview = (file) => {
    selectedFile.value = file
    previewFile.value = file
    dialogs.filePreview = true
  }

  const onItemRename = (item) => {
    selectedItem.value = item
    if (item.type === 'directory') {
      selectedDirectory.value = item
      dialogs.renameDirectory = true
    } else {
      dialogs.rename = true
    }
  }

  const onItemDelete = (item) => {
    // 删除操作已在UnifiedFileList组件中处理
    refreshData()
  }

  const onItemMove = (item) => {
    selectedItem.value = item
    if (item.type === 'directory') {
      selectedDirectory.value = item
      dialogs.moveDirectory = true
    } else {
      dialogs.move = true
    }
  }

  // 对话框事件处理
  const onDirectoryCreated = () => {
    dialogs.createDirectory = false
    refreshData()
  }

  const onDirectoryRenamed = () => {
    dialogs.renameDirectory = false
    selectedDirectory.value = null
    refreshData()
    $baseMessage('目录重命名成功', 'success', 'vab-hey-message-success')
  }

  const onDirectoryMoved = () => {
    dialogs.moveDirectory = false
    selectedDirectory.value = null
    refreshData()
    $baseMessage('目录移动成功', 'success', 'vab-hey-message-success')
  }

  const onItemRenamed = () => {
    dialogs.rename = false
    selectedItem.value = null
    refreshData()
    $baseMessage('重命名成功', 'success', 'vab-hey-message-success')
  }

  const onItemMoved = () => {
    dialogs.move = false
    selectedItem.value = null
    refreshData()
    $baseMessage('移动成功', 'success', 'vab-hey-message-success')
  }

  const onFileUploaded = () => {
    dialogs.fileUpload = false
    refreshData()
  }

  const closeFilePreview = () => {
    previewFile.value = null
    dialogs.filePreview = false
  }

  // 导航历史记录管理方法
  const addToHistory = (directoryId) => {
    // 如果当前不在历史记录的最后，则删除当前位置之后的所有记录
    if (currentHistoryIndex.value < navigationHistory.value.length - 1) {
      navigationHistory.value = navigationHistory.value.slice(
        0,
        currentHistoryIndex.value + 1
      )
    }

    // 避免重复添加相同的目录ID
    if (navigationHistory.value[currentHistoryIndex.value] !== directoryId) {
      navigationHistory.value.push(directoryId)
      currentHistoryIndex.value = navigationHistory.value.length - 1
    }
  }

  // 处理面包屑导航事件
  const handleBreadcrumbNavigation = (action) => {
    if (action === 'back') {
      goBack()
    } else if (action === 'forward') {
      goForward()
    }
  }

  // 后退导航
  const goBack = async () => {
    if (currentHistoryIndex.value > 0) {
      currentHistoryIndex.value--
      currentDirectoryId.value =
        navigationHistory.value[currentHistoryIndex.value]
      await updateBreadcrumb()
      refreshData()
    }
  }

  // 前进导航
  const goForward = async () => {
    if (currentHistoryIndex.value < navigationHistory.value.length - 1) {
      currentHistoryIndex.value++
      currentDirectoryId.value =
        navigationHistory.value[currentHistoryIndex.value]
      await updateBreadcrumb()
      refreshData()
    }
  }

  // 生命周期
  onMounted(async () => {
    // 初始化导航历史记录
    addToHistory(currentDirectoryId.value)
    await updateBreadcrumb()
    refreshData()
  })
</script>

<style lang="scss" scoped>
  .fms-main {
    height: 100vh;

    &__container {
      height: 100%;
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      border-bottom: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
    }

    &__toolbar {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    &__search {
      width: 300px;
    }

    &__toolbar {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    &__body {
      height: calc(100% - 60px);
    }

    &__aside {
      border-right: 1px solid var(--el-border-color-light);
      background: var(--el-bg-color);
      overflow: hidden;
    }

    &__main {
      padding: 16px;
      overflow: auto;
    }
  }
</style>
