/**
 * FMS 文件状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as fileApi from '../../../api/fms/files'

export const useFileStore = defineStore('fmsFile', () => {
  // 状态
  const files = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const pagination = ref({
    total: 0,
    page: 1,
    pageSize: 20,
  })

  // 上传状态
  const uploadQueue = ref([])
  const isUploading = ref(false)
  const uploadProgress = ref({})

  // 缓存
  const fileCache = ref(new Map())
  const versionCache = ref(new Map())

  // 计算属性
  const fileMap = computed(() => {
    const map = new Map()
    files.value.forEach((file) => {
      map.set(file.id, file)
    })
    return map
  })

  const hasFiles = computed(() => files.value.length > 0)

  const uploadingFiles = computed(() =>
    uploadQueue.value.filter((item) => item.status === 'uploading')
  )

  const completedUploads = computed(() =>
    uploadQueue.value.filter((item) => item.status === 'success')
  )

  const failedUploads = computed(() =>
    uploadQueue.value.filter((item) => item.status === 'error')
  )

  // 方法
  const fetchFiles = async (params = {}) => {
    try {
      isLoading.value = true
      error.value = null

      const response = await fileApi.list(params)

      files.value = response.data
      pagination.value = {
        total: response.total,
        page: response.page,
        pageSize: response.pageSize,
      }

      // 更新缓存
      response.data.forEach((file) => {
        fileCache.value.set(file.id, file)
      })

      return response
    } catch (err) {
      error.value = err.message || '获取文件列表失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const getFile = async (id) => {
    // 先从缓存获取
    if (fileCache.value.has(id)) {
      return fileCache.value.get(id)
    }

    try {
      const file = await fileApi.show(id)
      fileCache.value.set(id, file)
      return file
    } catch (err) {
      error.value = err.message || '获取文件信息失败'
      throw err
    }
  }

  const uploadFile = async (data, onProgress) => {
    try {
      const uploadId = Date.now() + Math.random()

      // 添加到上传队列
      const uploadItem = {
        id: uploadId,
        filename: data.file.name,
        size: data.file.size,
        status: 'uploading',
        progress: 0,
        error: null,
      }

      uploadQueue.value.push(uploadItem)
      isUploading.value = true

      // 上传进度回调
      const progressCallback = (progress) => {
        uploadItem.progress = progress.percent
        uploadProgress.value[uploadId] = progress
        if (onProgress) onProgress(progress)
      }

      const response = await fileApi.upload(data, progressCallback)
      const newFile = response.file

      // 更新上传状态
      uploadItem.status = 'success'
      uploadItem.fileId = newFile.id

      // 添加到文件列表
      files.value.unshift(newFile)

      // 更新缓存
      fileCache.value.set(newFile.id, newFile)

      return newFile
    } catch (err) {
      // 更新上传状态
      const uploadItem = uploadQueue.value.find(
        (item) => item.filename === data.file.name
      )
      if (uploadItem) {
        uploadItem.status = 'error'
        uploadItem.error = err.message
      }

      error.value = err.message || '文件上传失败'
      throw err
    } finally {
      // 检查是否还有上传中的文件
      isUploading.value = uploadQueue.value.some(
        (item) => item.status === 'uploading'
      )
    }
  }

  const updateFile = async (id, data) => {
    try {
      isLoading.value = true
      error.value = null

      await fileApi.update(id, data)

      // 更新列表中的文件
      const index = files.value.findIndex((file) => file.id === id)
      if (index !== -1) {
        Object.assign(files.value[index], data)
        files.value[index].updatedAt = new Date().toISOString()
      }

      // 更新缓存
      if (fileCache.value.has(id)) {
        const cached = fileCache.value.get(id)
        Object.assign(cached, data)
        cached.updatedAt = new Date().toISOString()
      }

      return true
    } catch (err) {
      error.value = err.message || '更新文件失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteFile = async (id) => {
    try {
      isLoading.value = true
      error.value = null

      await fileApi.destroy(id)

      // 从列表中移除
      const index = files.value.findIndex((file) => file.id === id)
      if (index !== -1) {
        files.value.splice(index, 1)
      }

      // 从缓存中移除
      fileCache.value.delete(id)
      versionCache.value.delete(id)

      return true
    } catch (err) {
      error.value = err.message || '删除文件失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const restoreFile = async (id) => {
    try {
      isLoading.value = true
      error.value = null

      await fileApi.restore(id)

      // 清除缓存，重新获取数据
      fileCache.value.delete(id)

      return true
    } catch (err) {
      error.value = err.message || '恢复文件失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const downloadFile = async (id, version) => {
    try {
      const response = await fileApi.download(id, version)

      // 创建下载链接
      const link = document.createElement('a')
      link.href = response.url
      link.download = response.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      return response
    } catch (err) {
      error.value = err.message || '下载文件失败'
      throw err
    }
  }

  const getFileVersions = async (id) => {
    // 先从缓存获取
    if (versionCache.value.has(id)) {
      return versionCache.value.get(id)
    }

    try {
      const response = await fileApi.versions(id)
      versionCache.value.set(id, response)
      return response
    } catch (err) {
      error.value = err.message || '获取文件版本失败'
      throw err
    }
  }

  const revertFileVersion = async (id, version, comment) => {
    try {
      isLoading.value = true
      error.value = null

      await fileApi.revertVersion(id, version, { comment })

      // 清除版本缓存
      versionCache.value.delete(id)

      // 更新文件信息
      if (fileCache.value.has(id)) {
        fileCache.value.delete(id)
      }

      return true
    } catch (err) {
      error.value = err.message || '回滚文件版本失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateFileVisibility = async (id, visibility) => {
    try {
      isLoading.value = true
      error.value = null

      await fileApi.updateVisibility(id, { visibility })

      // 更新列表中的文件
      const index = files.value.findIndex((file) => file.id === id)
      if (index !== -1) {
        files.value[index].visibility = visibility
        files.value[index].updatedAt = new Date().toISOString()
      }

      // 更新缓存
      if (fileCache.value.has(id)) {
        const cached = fileCache.value.get(id)
        cached.visibility = visibility
        cached.updatedAt = new Date().toISOString()
      }

      return true
    } catch (err) {
      error.value = err.message || '更新文件可见性失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const clearUploadQueue = () => {
    uploadQueue.value = []
    uploadProgress.value = {}
  }

  const removeFromUploadQueue = (uploadId) => {
    const index = uploadQueue.value.findIndex((item) => item.id === uploadId)
    if (index !== -1) {
      uploadQueue.value.splice(index, 1)
    }
    delete uploadProgress.value[uploadId]
  }

  const refreshFiles = async (params = {}) => {
    // 清除缓存
    fileCache.value.clear()
    versionCache.value.clear()

    return await fetchFiles(params)
  }

  const clearCache = () => {
    fileCache.value.clear()
    versionCache.value.clear()
  }

  const setLoading = (loading) => {
    isLoading.value = loading
  }

  const setError = (err) => {
    error.value = err
  }

  const clearError = () => {
    error.value = null
  }

  // 工具方法
  const getFilesByDirectory = (directoryId) => {
    return files.value.filter((file) => file.directoryId === directoryId)
  }

  const getFilesByTag = (tagId) => {
    return files.value.filter(
      (file) => file.tags && file.tags.some((tag) => tag.id === tagId)
    )
  }

  const searchFiles = (keyword) => {
    if (!keyword) return files.value

    const lowerKeyword = keyword.toLowerCase()
    return files.value.filter(
      (file) =>
        file.name.toLowerCase().includes(lowerKeyword) ||
        file.originalName.toLowerCase().includes(lowerKeyword)
    )
  }

  return {
    // 状态
    files,
    isLoading,
    error,
    pagination,
    uploadQueue,
    isUploading,
    uploadProgress,

    // 计算属性
    fileMap,
    hasFiles,
    uploadingFiles,
    completedUploads,
    failedUploads,

    // 方法
    fetchFiles,
    getFile,
    uploadFile,
    updateFile,
    deleteFile,
    restoreFile,
    downloadFile,
    getFileVersions,
    revertFileVersion,
    updateFileVisibility,
    clearUploadQueue,
    removeFromUploadQueue,
    refreshFiles,
    clearCache,
    setLoading,
    setError,
    clearError,

    // 工具方法
    getFilesByDirectory,
    getFilesByTag,
    searchFiles,
  }
})
