<template>
  <div class="unified-file-list">
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button type="default" :icon="Refresh" @click="handleRefresh">
          刷新
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          type="primary"
          :icon="Upload"
          @click="handleUpload"
          v-if="props.directoryId"
        >
          上传文件
        </el-button>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      v-loading="loading"
      :data="computedTableData"
      @row-dblclick="handleRowDoubleClick"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%; height: calc(100% - 110px)"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column label="名称" min-width="300">
        <template #default="{ row }">
          <div class="unified-file-item">
            <el-icon class="unified-file-icon" :size="20">
              <FolderOpened v-if="row.item_type === 'directory'" />
              <Document v-else-if="isDocument(row.mime_type)" />
              <Picture v-else-if="isImage(row.mime_type)" />
              <VideoPlay v-else-if="isVideo(row.mime_type)" />
              <Headset v-else-if="isAudio(row.mime_type)" />
              <Files v-else />
            </el-icon>
            <span class="unified-file-name">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="大小" width="120">
        <template #default="{ row }">
          <span v-if="row.item_type === 'directory'">-</span>
          <span v-else>{{ formatFileSize(row.size) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="修改时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="80" fixed="right">
        <template #default="{ row }">
          <!-- 文件夹操作 -->
          <el-dropdown v-if="row.item_type === 'directory'" trigger="click">
            <span class="unified-file-list-more-icon">
              <vab-icon
                icon="more"
                is-custom-svg
                :style="{
                  width: 16 + 'px',
                  height: 16 + 'px',
                }"
              />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleRowDoubleClick(row)">
                  <el-icon><FolderOpened /></el-icon>
                  进入文件夹
                </el-dropdown-item>
                <el-dropdown-item @click="handleRename(row)">
                  重命名
                </el-dropdown-item>
                <el-dropdown-item @click="handleMove(row)">
                  移动
                </el-dropdown-item>
                <el-dropdown-item @click="handleDelete(row)" divided>
                  <el-icon color="#ff4d4f"><Delete /></el-icon>
                  <span style="color: #ff4d4f">删除</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 文件操作 -->
          <el-dropdown v-else trigger="click">
            <span class="unified-file-list-more-icon">
              <vab-icon
                icon="more"
                is-custom-svg
                :style="{
                  width: 16 + 'px',
                  height: 16 + 'px',
                }"
              />
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handlePreview(row)">
                  <el-icon><View /></el-icon>
                  预览
                </el-dropdown-item>
                <el-dropdown-item @click="handleDownload(row)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-dropdown-item>
                <el-dropdown-item @click="handleRename(row)">
                  重命名
                </el-dropdown-item>
                <el-dropdown-item @click="handleMove(row)">
                  移动
                </el-dropdown-item>
                <el-dropdown-item @click="handleDelete(row)" divided>
                  <el-icon color="#ff4d4f"><Delete /></el-icon>
                  <span style="color: #ff4d4f">删除</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 统计信息和分页 -->
    <div class="unified-file-list-footer-wrapper" v-if="total > 0">
      <div class="unified-file-list-pagination-wrapper">
        <el-pagination
          background
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div class="unified-file-list-stats-info">
        <span class="unified-file-list-stats-item">
          文件夹: {{ directoryCount }}
        </span>
        <span class="unified-file-list-stats-item">文件: {{ fileCount }}</span>
        <span class="unified-file-list-stats-item">总计: {{ total }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch, inject } from 'vue'
  import {
    FolderOpened,
    Document,
    Picture,
    VideoPlay,
    Headset,
    Files,
    Upload,
    Refresh,
    View,
    Download,
    Delete,
    ArrowDown,
  } from '@element-plus/icons-vue'
  import * as directoryApi from '@/api/fms/directories'
  import * as fileApi from '@/api/fms/files'

  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  const props = defineProps({
    directoryId: {
      type: [Number, String],
      default: null,
    },
    searchKeyword: {
      type: String,
      default: '',
    },
  })

  const emit = defineEmits([
    'refresh',
    'directory-enter',
    'file-preview',
    'item-rename',
    'item-delete',
    'item-move',
    'upload',
  ])

  // 工具栏操作
  const handleUpload = () => {
    emit('upload')
  }

  // 响应式数据
  const loading = ref(false)
  const tableData = ref([])
  const selectedItems = ref([])
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const directoryCount = ref(0)
  const fileCount = ref(0)

  // 计算属性
  const computedTableData = computed(() => {
    return tableData.value
  })

  const handleRefresh = () => {
    loadData()
  }

  // 方法
  const loadData = async () => {
    loading.value = true
    const start = Date.now()

    try {
      const params = {
        parent_id: props.directoryId,
        search: props.searchKeyword,
        page: currentPage.value,
        page_size: pageSize.value,
        union: 1, // 启用联合查询
      }

      const response = await directoryApi.list(params)
      const responseData = response.data || response

      // 处理联合查询返回的数据
      tableData.value = responseData.data || []
      total.value = responseData.total || 0
      directoryCount.value = responseData.directory_count || 0
      fileCount.value = responseData.file_count || 0

      const duration = Date.now() - start
      if (duration < 500) {
        await new Promise((resolve) => setTimeout(resolve, 500 - duration))
      }
    } catch (error) {
      $baseMessage(
        '加载数据失败：' + error.message,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleRowDoubleClick = (row) => {
    if (row.item_type === 'directory') {
      emit('directory-enter', row)
    } else {
      emit('file-preview', row)
    }
  }

  const handleSelectionChange = (selection) => {
    selectedItems.value = selection
  }

  const handleDownload = async (row) => {
    try {
      const result = await fileApi.download(row.id)
      const link = document.createElement('a')
      link.href = result.url
      link.download = result.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(result.url)
    } catch (error) {
      $baseMessage(
        '下载失败：' + error.message,
        'error',
        'vab-hey-message-error'
      )
    }
  }

  const handlePreview = (row) => {
    emit('file-preview', row)
  }

  const handleRename = (row) => {
    emit('item-rename', row)
  }

  const handleMove = (row) => {
    emit('item-move', row)
  }

  const handleDelete = async (row) => {
    $baseConfirm(
      `确定要删除${row.item_type === 'directory' ? '文件夹' : '文件'} "${
        row.name
      }" 吗？`,
      null,
      async () => {
        try {
          if (row.item_type === 'directory') {
            await directoryApi.destroy(row.id)
          } else {
            await fileApi.destroy(row.id)
          }

          $baseMessage('删除成功', 'success', 'vab-hey-message-success')
          emit('item-delete', row)
          loadData()
        } catch (error) {
          $baseMessage(
            '删除失败：' + error.message,
            'error',
            'vab-hey-message-error'
          )
        }
      }
    )
  }

  const handleSizeChange = (size) => {
    pageSize.value = size
    loadData()
  }

  const handleCurrentChange = (page) => {
    currentPage.value = page
    loadData()
  }

  // 工具函数
  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return dateTime
  }

  const isDocument = (mimeType) => {
    return (
      mimeType &&
      (mimeType.includes('text/') ||
        mimeType.includes('application/pdf') ||
        mimeType.includes('application/msword') ||
        mimeType.includes('application/vnd.openxmlformats'))
    )
  }

  const isImage = (mimeType) => {
    return mimeType && mimeType.startsWith('image/')
  }

  const isVideo = (mimeType) => {
    return mimeType && mimeType.startsWith('video/')
  }

  const isAudio = (mimeType) => {
    return mimeType && mimeType.startsWith('audio/')
  }

  // 监听属性变化
  watch(
    [() => props.directoryId, () => props.searchKeyword],
    () => {
      loadData()
    },
    { immediate: true }
  )

  // 暴露方法
  defineExpose({
    loadData,
    selectedItems,
  })
</script>

<style scoped>
  :deep() {
    .el-row {
      flex-wrap: nowrap;
    }
  }

  .unified-file-list {
    height: 100%;
  }

  .unified-file-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .unified-file-icon {
    flex-shrink: 0;
  }

  .unified-file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .unified-file-list-footer-wrapper {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .unified-file-list-stats-info {
    display: flex;
    gap: 16px;
    margin: 12px 0 0 0;
    color: #666;
    font-size: 14px;
  }

  .unified-file-list-stats-item {
    padding: 4px 8px;
    background: #f5f5f5;
    border-radius: 4px;
  }

  .unified-file-list-pagination-wrapper {
    display: flex;
    justify-content: flex-start;
  }
</style>
