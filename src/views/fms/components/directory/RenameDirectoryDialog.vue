<template>
  <firefly-dialog
    v-model="dialogVisible"
    title="重命名目录"
    width="450px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="当前名称">
        <el-input :value="originalName" readonly disabled>
          <template #prefix>
            <vab-icon
              :icon="'icon-dir'"
              is-custom-svg
              :style="{
                width: 16 + 'px',
                height: 16 + 'px',
                marginRight: '6px',
              }"
            />
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="新名称" prop="name">
        <el-input
          ref="nameInputRef"
          v-model="formData.name"
          placeholder="请输入新的目录名称"
          maxlength="100"
          show-word-limit
          @keyup.enter="handleSubmit"
          @focus="handleInputFocus"
        >
          <template #prefix>
            <el-icon><Edit /></el-icon>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item>
        <div class="rename-directory-dialog__tips">
          <el-alert type="info" :closable="false" show-icon>
            <template #title>
              <div class="rename-directory-dialog__tips-content">
                <div>重命名注意事项：</div>
                <ul>
                  <li>目录名称不能包含特殊字符：&lt; &gt; : " / \ | ? *</li>
                  <li>不能以点（.）开头或结尾</li>
                  <li>重命名后可能影响已分享的链接</li>
                </ul>
              </div>
            </template>
          </el-alert>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="rename-directory-dialog__footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="isSubmitting"
          :disabled="!hasChanged"
          @click="handleSubmit"
        >
          确认重命名
        </el-button>
      </div>
    </template>
  </firefly-dialog>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    watch,
    nextTick,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import { Folder, Edit } from '@element-plus/icons-vue'
  import { useDirectoryStore } from '../../stores/directoryStore'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      default: null,
    },
    name: {
      type: String,
      default: '',
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'success'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // Store
  const directoryStore = useDirectoryStore()

  // 响应式数据
  const formRef = ref()
  const nameInputRef = ref()
  const isSubmitting = ref(false)
  const originalName = ref('')

  const formData = reactive({
    name: '',
  })

  // 表单验证规则
  const formRules = {
    name: [{ required: true, validator: validateName, trigger: 'blur' }],
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  const hasChanged = computed(() => {
    return (
      formData.name.trim() !== originalName.value && formData.name.trim() !== ''
    )
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible) {
        resetForm()
        // 延迟聚焦输入框
        nextTick(() => {
          if (nameInputRef.value) {
            nameInputRef.value.focus()
          }
        })
      }
    }
  )

  // 监听名称变化
  watch(
    () => props.name,
    (newName) => {
      originalName.value = newName || ''
      formData.name = newName || ''
    }
  )

  // 方法
  const resetForm = () => {
    originalName.value = props.name || ''
    formData.name = props.name || ''

    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  const handleInputFocus = () => {
    // 选中文件名部分（不包括扩展名）
    nextTick(() => {
      if (nameInputRef.value && nameInputRef.value.input) {
        const input = nameInputRef.value.input
        const name = formData.name
        const lastDotIndex = name.lastIndexOf('.')

        if (lastDotIndex > 0) {
          // 如果有扩展名，只选中文件名部分
          input.setSelectionRange(0, lastDotIndex)
        } else {
          // 没有扩展名，选中全部
          input.select()
        }
      }
    })
  }

  const handleSubmit = async () => {
    if (!formRef.value || !hasChanged.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      isSubmitting.value = true

      const newName = formData.name.trim()

      await directoryStore.renameDirectory(props.id, newName)

      $baseMessage('目录重命名成功', 'success', 'vab-hey-message-success')

      emit('success', {
        id: props.id,
        oldName: originalName.value,
        newName: newName,
      })

      handleClose()
    } catch (error) {
      $baseMessage(
        error.message || '目录重命名失败',
        'error',
        'vab-hey-message-error'
      )
    } finally {
      isSubmitting.value = false
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
  }

  // 表单验证方法
  function validateName(rule, value, callback) {
    if (!value || !value.trim()) {
      callback(new Error('请输入目录名称'))
      return
    }

    const trimmedValue = value.trim()

    if (trimmedValue === originalName.value) {
      callback(new Error('新名称不能与原名称相同'))
      return
    }

    if (trimmedValue.length < 1 || trimmedValue.length > 100) {
      callback(new Error('目录名称长度在 1 到 100 个字符'))
      return
    }

    // 检查特殊字符
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(trimmedValue)) {
      callback(new Error('目录名称不能包含特殊字符 < > : " / \\ | ? *'))
      return
    }

    // 检查是否以点开头或结尾
    if (trimmedValue.startsWith('.') || trimmedValue.endsWith('.')) {
      callback(new Error('目录名称不能以点开头或结尾'))
      return
    }

    // 检查保留名称
    const reservedNames = [
      'CON',
      'PRN',
      'AUX',
      'NUL',
      'COM1',
      'COM2',
      'COM3',
      'COM4',
      'COM5',
      'COM6',
      'COM7',
      'COM8',
      'COM9',
      'LPT1',
      'LPT2',
      'LPT3',
      'LPT4',
      'LPT5',
      'LPT6',
      'LPT7',
      'LPT8',
      'LPT9',
    ]
    if (reservedNames.includes(trimmedValue.toUpperCase())) {
      callback(new Error('不能使用系统保留名称'))
      return
    }

    callback()
  }
</script>

<style lang="scss" scoped>
  .rename-directory-dialog {
    &__tips {
      margin-top: 8px;

      &-content {
        font-size: 13px;
        line-height: 1.5;

        ul {
          margin: 8px 0 0 0;
          padding-left: 16px;

          li {
            margin-bottom: 4px;
            color: var(--el-text-color-regular);
          }
        }
      }
    }

    &__footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  :deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: var(--el-fill-color-light);
  }

  :deep(.el-alert) {
    padding: 12px;

    .el-alert__content {
      padding-left: 8px;
    }

    .el-alert__title {
      margin-bottom: 0;
    }
  }
</style>
