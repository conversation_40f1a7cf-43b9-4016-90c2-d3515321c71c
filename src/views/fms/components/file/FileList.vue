<template>
  <div class="fms-file-list">
    <div class="fms-file-list__toolbar">
      <div class="fms-file-list__toolbar-left">
        <el-button
          type="primary"
          :icon="Upload"
          @click="handleUpload"
          v-fms-permission="{
            targetType: 'directory',
            targetId: directoryId,
            permission: FMS_PERMISSIONS.UPLOAD,
          }"
        >
          上传文件
        </el-button>
        <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
        <el-button
          v-if="selectedFiles.length > 0"
          :icon="Download"
          @click="handleBatchDownload"
        >
          批量下载
        </el-button>
        <el-button
          v-if="selectedFiles.length > 0"
          type="danger"
          :icon="Delete"
          @click="handleBatchDelete"
          v-fms-permission="{
            targetType: 'directory',
            targetId: directoryId,
            permission: FMS_PERMISSIONS.DELETE,
          }"
        >
          批量删除
        </el-button>
      </div>

      <div class="fms-file-list__toolbar-right">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="list">
            <el-icon><List /></el-icon>
            列表
          </el-radio-button>
          <el-radio-button label="grid">
            <el-icon><Grid /></el-icon>
            网格
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'" class="fms-file-list__table">
      <el-table
        ref="tableRef"
        :data="files"
        v-loading="isLoading"
        row-key="id"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="文件名" min-width="250">
          <template #default="{ row }">
            <div class="fms-file-list__name">
              <div class="fms-file-list__icon">
                <el-image
                  v-if="row.thumbnail"
                  :src="row.thumbnail"
                  :alt="row.name"
                  fit="cover"
                  class="fms-file-list__thumbnail"
                />
                <el-icon v-else class="fms-file-list__file-icon">
                  <component :is="getFileIcon(row)" />
                </el-icon>
              </div>
              <div class="fms-file-list__info">
                <div class="fms-file-list__title">{{ row.name }}</div>
                <div class="fms-file-list__meta">
                  <span class="fms-file-list__extension">
                    {{ row.extension?.toUpperCase() }}
                  </span>
                  <el-tag
                    v-if="row.visibility === 'private'"
                    type="warning"
                    size="small"
                  >
                    私有
                  </el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="大小" width="100" align="right">
          <template #default="{ row }">
            <span class="fms-file-list__size">
              {{ formatFileSize(row.size) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="getFileTypeTag(row.category)" size="small">
              {{ getFileTypeLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="标签" width="150">
          <template #default="{ row }">
            <div class="fms-file-list__tags">
              <el-tag
                v-for="tag in row.tags?.slice(0, 2)"
                :key="tag.id"
                size="small"
                :color="tag.color"
                class="fms-file-list__tag"
              >
                {{ tag.name }}
              </el-tag>
              <el-tag v-if="row.tags?.length > 2" size="small" type="info">
                +{{ row.tags.length - 2 }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="上传者" width="120">
          <template #default="{ row }">
            <div class="fms-file-list__creator">
              <el-avatar
                v-if="row.creator?.avatar"
                :src="row.creator.avatar"
                :size="24"
              />
              <span>{{ row.creator?.name || '-' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="上传时间" width="160">
          <template #default="{ row }">
            <span class="fms-file-list__time">
              {{ formatDateTime(row.createdAt) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="fms-file-list__actions">
              <el-button
                type="text"
                size="small"
                @click.stop="handlePreview(row)"
                v-if="canPreview(row)"
              >
                预览
              </el-button>
              <el-button
                type="text"
                size="small"
                @click.stop="handleDownload(row)"
                v-fms-permission="{
                  targetType: 'file',
                  targetId: row.id,
                  permission: FMS_PERMISSIONS.VIEW,
                }"
              >
                下载
              </el-button>
              <el-dropdown @command="(command) => handleCommand(command, row)">
                <el-button type="text" size="small">
                  更多
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                    <el-dropdown-item command="versions">
                      版本历史
                    </el-dropdown-item>
                    <el-dropdown-item command="share">
                      分享文件
                    </el-dropdown-item>
                    <el-dropdown-item command="move">移动文件</el-dropdown-item>
                    <el-dropdown-item
                      command="delete"
                      divided
                      v-fms-permission="{
                        targetType: 'file',
                        targetId: row.id,
                        permission: FMS_PERMISSIONS.DELETE,
                      }"
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 网格视图 -->
    <div v-else class="fms-file-list__grid">
      <div
        v-for="file in files"
        :key="file.id"
        class="fms-file-list__grid-item"
        @click="handleRowClick(file)"
        @dblclick="handlePreview(file)"
      >
        <div class="fms-file-list__grid-preview">
          <el-image
            v-if="file.thumbnail"
            :src="file.thumbnail"
            :alt="file.name"
            fit="cover"
            class="fms-file-list__grid-thumbnail"
          />
          <div v-else class="fms-file-list__grid-icon">
            <el-icon>
              <component :is="getFileIcon(file)" />
            </el-icon>
          </div>
        </div>
        <div class="fms-file-list__grid-content">
          <div class="fms-file-list__grid-title" :title="file.name">
            {{ file.name }}
          </div>
          <div class="fms-file-list__grid-info">
            <span>{{ formatFileSize(file.size) }}</span>
            <span>{{ formatDateTime(file.createdAt) }}</span>
          </div>
        </div>
        <div class="fms-file-list__grid-actions">
          <el-dropdown @command="(command) => handleCommand(command, file)">
            <el-button type="text" :icon="MoreFilled" size="small" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="preview" v-if="canPreview(file)">
                  预览
                </el-dropdown-item>
                <el-dropdown-item command="download">下载</el-dropdown-item>
                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                <el-dropdown-item command="delete" divided>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!isLoading && files.length === 0" class="fms-file-list__empty">
      <el-empty description="暂无文件数据">
        <el-button
          type="primary"
          :icon="Upload"
          @click="handleUpload"
          v-fms-permission="{
            targetType: 'directory',
            targetId: directoryId,
            permission: FMS_PERMISSIONS.UPLOAD,
          }"
        >
          上传文件
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    watch,
    onMounted,
    inject,
    defineProps,
    defineEmits,
  } from 'vue'
  import {
    Upload,
    Download,
    Delete,
    Refresh,
    List,
    Grid,
    ArrowDown,
    MoreFilled,
  } from '@element-plus/icons-vue'
  import { useFileStore } from '../../stores/fileStore'
  import { useFmsStore } from '../../stores/fmsStore'
  import { useFmsPermission, vFmsPermission } from '@/utils/bmsPermission'

  // Props 定义
  const props = defineProps({
    directoryId: {
      type: [Number, String],
      default: null,
    },
    filters: {
      type: Object,
      default: () => ({}),
    },
  })

  // Events 定义
  const emit = defineEmits(['refresh', 'preview', 'delete', 'upload', 'select'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  // Store
  const fileStore = useFileStore()
  const fmsStore = useFmsStore()
  const { FMS_PERMISSIONS } = useFmsPermission()

  // 响应式数据
  const tableRef = ref()
  const viewMode = ref('list')
  const selectedFiles = ref([])

  // 计算属性
  const isLoading = computed(() => fileStore.isLoading)
  const files = computed(() => fileStore.files)

  // 监听目录变化
  watch(
    () => props.directoryId,
    () => {
      loadFiles()
    }
  )

  // 监听筛选条件变化
  watch(
    () => props.filters,
    () => {
      loadFiles()
    },
    { deep: true }
  )

  // 方法
  const loadFiles = async () => {
    try {
      await fileStore.fetchFiles({
        directoryId: props.directoryId,
        ...props.filters,
        includeTags: true,
        includeCreator: true,
      })
    } catch (error) {
      $baseMessage('加载文件列表失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleUpload = () => {
    emit('upload', { directoryId: props.directoryId })
  }

  const handleRefresh = () => {
    emit('refresh')
    loadFiles()
  }

  const handleSelectionChange = (selection) => {
    selectedFiles.value = selection
  }

  const handleRowClick = (row) => {
    emit('select', row)
  }

  const handlePreview = (row) => {
    emit('preview', row)
  }

  const handleDownload = async (row) => {
    try {
      await fileStore.downloadFile(row.id)
      $baseMessage('文件下载开始', 'success', 'vab-hey-message-success')
    } catch (error) {
      $baseMessage('文件下载失败', 'error', 'vab-hey-message-error')
    }
  }

  const handleBatchDownload = () => {
    // TODO: 实现批量下载
    $baseMessage('批量下载功能开发中', 'info', 'vab-hey-message-info')
  }

  const handleBatchDelete = () => {
    $baseConfirm(
      `确认删除选中的 ${selectedFiles.value.length} 个文件？`,
      () => {},
      async () => {
        try {
          const promises = selectedFiles.value.map((file) =>
            fileStore.deleteFile(file.id)
          )
          await Promise.all(promises)

          $baseMessage('批量删除成功', 'success', 'vab-hey-message-success')
          selectedFiles.value = []
          await loadFiles()
        } catch (error) {
          $baseMessage('批量删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  const handleCommand = (command, row) => {
    switch (command) {
      case 'preview':
        handlePreview(row)
        break
      case 'download':
        handleDownload(row)
        break
      case 'edit':
        handleEdit(row)
        break
      case 'versions':
        handleVersions(row)
        break
      case 'share':
        handleShare(row)
        break
      case 'move':
        handleMove(row)
        break
      case 'delete':
        handleDelete(row)
        break
    }
  }

  const handleEdit = (row) => {
    // TODO: 实现文件信息编辑
    $baseMessage('文件编辑功能开发中', 'info', 'vab-hey-message-info')
  }

  const handleVersions = (row) => {
    // TODO: 实现版本历史
    $baseMessage('版本历史功能开发中', 'info', 'vab-hey-message-info')
  }

  const handleShare = (row) => {
    // TODO: 实现文件分享
    $baseMessage('文件分享功能开发中', 'info', 'vab-hey-message-info')
  }

  const handleMove = (row) => {
    // TODO: 实现文件移动
    $baseMessage('文件移动功能开发中', 'info', 'vab-hey-message-info')
  }

  const handleDelete = (row) => {
    $baseConfirm(
      `确认删除文件 "${row.name}"？删除后将移至回收站。`,
      () => {},
      async () => {
        try {
          await fileStore.deleteFile(row.id)
          $baseMessage('文件删除成功', 'success', 'vab-hey-message-success')
          emit('delete', row)
          await loadFiles()
        } catch (error) {
          $baseMessage('文件删除失败', 'error', 'vab-hey-message-error')
        }
      }
    )
  }

  // 工具方法
  const formatFileSize = (bytes) => {
    return fmsStore.formatFileSize(bytes)
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  const getFileIcon = (file) => {
    return fmsStore.getFileIcon(file)
  }

  const canPreview = (file) => {
    return fmsStore.canPreviewFile(file)
  }

  const getFileTypeTag = (category) => {
    const typeMap = {
      image: 'success',
      video: 'primary',
      audio: 'warning',
      document: 'info',
      archive: 'danger',
      code: '',
      other: 'info',
    }
    return typeMap[category] || 'info'
  }

  const getFileTypeLabel = (category) => {
    const labelMap = {
      image: '图片',
      video: '视频',
      audio: '音频',
      document: '文档',
      archive: '压缩包',
      code: '代码',
      other: '其他',
    }
    return labelMap[category] || '其他'
  }

  // 生命周期
  onMounted(() => {
    loadFiles()
  })

  // 暴露方法
  defineExpose({
    refresh: loadFiles,
    getSelectedFiles: () => selectedFiles.value,
  })
</script>

<style lang="scss" scoped>
  .fms-file-list {
    &__toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 12px 0;

      &-left,
      &-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    &__table {
      .el-table {
        border: 1px solid var(--el-border-color-light);
        border-radius: 6px;
      }
    }

    &__name {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    &__icon {
      flex-shrink: 0;
    }

    &__thumbnail {
      width: 32px;
      height: 32px;
      border-radius: 4px;
    }

    &__file-icon {
      font-size: 24px;
      color: var(--el-color-primary);
    }

    &__info {
      flex: 1;
      min-width: 0;
    }

    &__title {
      font-weight: 500;
      color: var(--el-text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &__meta {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 2px;
    }

    &__extension {
      font-size: 11px;
      color: var(--el-text-color-placeholder);
      background: var(--el-fill-color-light);
      padding: 1px 4px;
      border-radius: 2px;
    }

    &__size {
      font-family: 'Monaco', 'Menlo', monospace;
      color: var(--el-text-color-regular);
    }

    &__tags {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-wrap: wrap;
    }

    &__tag {
      max-width: 60px;

      :deep(.el-tag__content) {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &__creator {
      display: flex;
      align-items: center;
      gap: 6px;

      span {
        font-size: 13px;
        color: var(--el-text-color-regular);
      }
    }

    &__time {
      font-size: 13px;
      color: var(--el-text-color-regular);
    }

    &__actions {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    // 网格视图样式
    &__grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 16px;
      padding: 16px 0;
    }

    &__grid-item {
      position: relative;
      padding: 12px;
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .fms-file-list__grid-actions {
          opacity: 1;
        }
      }
    }

    &__grid-preview {
      text-align: center;
      margin-bottom: 8px;
    }

    &__grid-thumbnail {
      width: 60px;
      height: 60px;
      border-radius: 6px;
    }

    &__grid-icon {
      .el-icon {
        font-size: 48px;
        color: var(--el-color-primary);
      }
    }

    &__grid-content {
      text-align: center;
    }

    &__grid-title {
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 13px;
    }

    &__grid-info {
      font-size: 11px;
      color: var(--el-text-color-regular);

      span {
        display: block;
        margin-bottom: 2px;
      }
    }

    &__grid-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      opacity: 0;
      transition: opacity 0.2s;
    }

    &__empty {
      padding: 40px 0;
      text-align: center;
    }
  }
</style>
