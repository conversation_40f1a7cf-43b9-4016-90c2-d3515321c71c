<template>
  <firefly-dialog
    v-model="dialogVisible"
    :title="file?.name || '文件预览'"
    width="80%"
    :close-on-click-modal="false"
    custom-class="unified-file-preview-dialog"
    @close="handleClose"
  >
    <div class="unified-file-preview" v-loading="isLoading">
      <div class="unified-file-preview__header">
        <div class="unified-file-preview__info">
          <div class="unified-file-preview__name">
            {{ file?.name }}
          </div>
          <div class="unified-file-preview__meta">
            <span>{{ formatFileSize(file?.size) }}</span>
            <span>
              {{ getFileExtension(file?.name)?.toUpperCase() }}
            </span>
            <span>
              {{ formatDateTime(file?.created_at || file?.createdAt) }}
            </span>
          </div>
        </div>
        <div class="unified-file-preview__actions">
          <el-button type="text" :icon="Download" @click="handleDownload">
            下载
          </el-button>
        </div>
      </div>

      <div class="unified-file-preview__content" ref="previewContentRef">
        <!-- 图片预览 -->
        <div v-if="previewType === 'image'" class="unified-file-preview__image">
          <el-image
            :src="previewUrl"
            :alt="file?.name"
            fit="contain"
            :preview-src-list="[previewUrl]"
            :initial-index="0"
            class="unified-file-preview__image-viewer"
          />
        </div>

        <!-- 视频预览 -->
        <div
          v-else-if="previewType === 'video'"
          class="unified-file-preview__video"
        >
          <video
            ref="videoRef"
            :src="previewUrl"
            controls
            preload="metadata"
            class="unified-file-preview__video-player"
          >
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 音频预览 -->
        <div
          v-else-if="previewType === 'audio'"
          class="unified-file-preview__audio"
        >
          <div class="unified-file-preview__audio-cover">
            <el-icon><Headset /></el-icon>
            <div class="unified-file-preview__audio-title">
              {{ file?.name }}
            </div>
          </div>
          <audio
            ref="audioRef"
            :src="previewUrl"
            controls
            preload="metadata"
            class="unified-file-preview__audio-player"
          >
            您的浏览器不支持音频播放
          </audio>
        </div>

        <!-- PDF预览 -->
        <div
          v-else-if="previewType === 'pdf'"
          class="unified-file-preview__pdf"
        >
          <iframe
            :src="previewUrl"
            class="unified-file-preview__pdf-viewer"
            frameborder="0"
          />
        </div>

        <!-- 文本预览 -->
        <div
          v-else-if="previewType === 'text'"
          class="unified-file-preview__text"
        >
          <pre class="unified-file-preview__text-content">{{
            textContent
          }}</pre>
        </div>

        <!-- 不支持预览 -->
        <div v-else class="unified-file-preview__unsupported">
          <div class="unified-file-preview__unsupported-placeholder">
            <el-icon><Files /></el-icon>
            <div class="unified-file-preview__unsupported-title">
              无法预览此文件
            </div>
            <div class="unified-file-preview__unsupported-hint">
              该文件类型暂不支持在线预览，请下载后查看
            </div>
            <el-button type="primary" @click="handleDownload">
              下载文件
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </firefly-dialog>
</template>

<script setup>
  import { ref, computed, watch, inject } from 'vue'
  import { Download, Headset, Files } from '@element-plus/icons-vue'
  import * as fileApi from '@/api/fms/files'

  // Props 定义
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    file: {
      type: Object,
      default: null,
    },
  })

  // Events 定义
  const emit = defineEmits(['update:visible', 'download'])

  // 注入服务
  const $baseMessage = inject('$baseMessage')

  // 响应式数据
  const previewContentRef = ref()
  const videoRef = ref()
  const audioRef = ref()
  const isLoading = ref(false)
  const textContent = ref('')
  const previewUrl = ref('')

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  })

  // 预览类型计算
  const previewType = computed(() => {
    if (!props.file) return 'unsupported'

    const mimeType = props.file.mime_type || props.file.mimeType
    const extension = getFileExtension(props.file.name)?.toLowerCase()

    if (mimeType?.startsWith('image/')) return 'image'
    if (mimeType?.startsWith('video/')) return 'video'
    if (mimeType?.startsWith('audio/')) return 'audio'
    if (mimeType === 'application/pdf' || extension === 'pdf') return 'pdf'
    if (
      mimeType?.startsWith('text/') ||
      ['txt', 'md', 'json', 'xml', 'csv'].includes(extension)
    ) {
      return 'text'
    }

    return 'unsupported'
  })

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible && props.file) {
        loadPreview()
      }
    }
  )

  // 监听文件变化
  watch(
    () => props.file,
    (file) => {
      if (file && props.visible) {
        loadPreview()
      }
    }
  )

  // 方法
  const loadPreview = async () => {
    if (!props.file) return

    try {
      isLoading.value = true

      // 构建预览URL - 支持多种字段名
      if (props.file.download_url || props.file.downloadUrl) {
        previewUrl.value = props.file.download_url || props.file.downloadUrl
      } else if (props.file.preview_url || props.file.previewUrl) {
        previewUrl.value = props.file.preview_url || props.file.previewUrl
      } else {
        // 如果没有直接的预览URL，通过API获取下载URL
        const result = await fileApi.download(props.file.id)
        previewUrl.value = result.url
      }

      // 如果是文本文件，加载内容
      if (previewType.value === 'text') {
        try {
          const response = await fetch(previewUrl.value)
          textContent.value = await response.text()
        } catch (error) {
          console.error('加载文本内容失败:', error)
          textContent.value = '无法加载文件内容'
        }
      }
    } catch (error) {
      $baseMessage(
        '加载预览失败：' + error.message,
        'error',
        'vab-hey-message-error'
      )
    } finally {
      isLoading.value = false
    }
  }

  const handleDownload = () => {
    emit('download', props.file)
  }

  const handleClose = () => {
    // 停止媒体播放
    if (videoRef.value) {
      videoRef.value.pause()
    }
    if (audioRef.value) {
      audioRef.value.pause()
    }

    dialogVisible.value = false
    previewUrl.value = ''
    textContent.value = ''
  }

  // 工具方法
  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return dateTime
  }

  // 获取文件扩展名
  const getFileExtension = (filename) => {
    if (!filename) return ''
    const lastDotIndex = filename.lastIndexOf('.')
    return lastDotIndex !== -1 ? filename.slice(lastDotIndex + 1) : ''
  }
</script>

<style scoped>
  /* 预览对话框样式 */
  .unified-file-preview {
    display: flex;
    flex-direction: column;
    height: 70vh;

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;
      border-bottom: 1px solid #e4e7ed;
    }

    &__info {
      flex: 1;
    }

    &__name {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }

    &__meta {
      font-size: 13px;
      color: #606266;

      span {
        margin-right: 16px;
      }
    }

    &__actions {
      display: flex;
      gap: 8px;
    }

    &__content {
      flex: 1;
      display: flex;
      overflow: hidden;
      margin: 16px 0;
    }

    /* 图片预览 */
    &__image {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &-viewer {
        max-width: 100%;
        max-height: 100%;
      }
    }

    /* 视频预览 */
    &__video {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &-player {
        max-width: 100%;
        max-height: 100%;
      }
    }

    /* 音频预览 */
    &__audio {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &-cover {
        text-align: center;
        margin-bottom: 20px;

        .el-icon {
          font-size: 64px;
          color: #409eff;
          margin-bottom: 12px;
        }
      }

      &-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 20px;
      }

      &-player {
        width: 100%;
        max-width: 400px;
      }
    }

    /* PDF预览 */
    &__pdf {
      flex: 1;

      &-viewer {
        width: 100%;
        height: 100%;
      }
    }

    /* 文本预览 */
    &__text {
      flex: 1;
      overflow: auto;

      &-content {
        padding: 16px;
        margin: 0;
        background: #f5f7fa;
        border-radius: 6px;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 13px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }

    /* 不支持预览的占位符 */
    &__unsupported {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &-placeholder {
        text-align: center;

        .el-icon {
          font-size: 64px;
          color: #c0c4cc;
          margin-bottom: 16px;
        }
      }

      &-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 8px;
      }

      &-hint {
        font-size: 14px;
        color: #606266;
        margin-bottom: 20px;
      }
    }
  }
</style>
