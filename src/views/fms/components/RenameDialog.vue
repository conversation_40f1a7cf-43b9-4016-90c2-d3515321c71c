<template>
  <el-dialog
    v-model="visible"
    :title="`重命名${item?.type === 'directory' ? '文件夹' : '文件'}`"
    width="500px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入新名称"
          @keyup.enter="handleSubmit"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, reactive, watch, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import * as directoryApi from '@/api/fms/directories'
  import * as fileApi from '@/api/fms/files'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: null,
    },
  })

  const emit = defineEmits(['update:modelValue', 'success'])

  const visible = ref(false)
  const loading = ref(false)
  const formRef = ref()

  const form = reactive({
    name: '',
  })

  const rules = {
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' },
      {
        min: 1,
        max: 255,
        message: '名称长度在 1 到 255 个字符',
        trigger: 'blur',
      },
    ],
  }

  // 监听显示状态
  watch(
    () => props.modelValue,
    (val) => {
      visible.value = val
      if (val && props.item) {
        form.name = props.item.name || ''
        nextTick(() => {
          formRef.value?.clearValidate()
        })
      }
    }
  )

  watch(visible, (val) => {
    emit('update:modelValue', val)
  })

  const handleClose = () => {
    visible.value = false
    form.name = ''
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      if (props.item?.type === 'directory') {
        await directoryApi.rename(props.item.id, { name: form.name })
      } else {
        await fileApi.update(props.item.id, { name: form.name })
      }

      ElMessage.success('重命名成功')
      emit('success')
      handleClose()
    } catch (error) {
      if (error.message) {
        ElMessage.error('重命名失败：' + error.message)
      }
    } finally {
      loading.value = false
    }
  }
</script>
