/**
 * FMS 权限相关类型定义
 */

import { SubjectType, ConditionType, RuleEffect } from './common'

// 权限位枚举
export enum PermissionBit {
  VIEW = 1, // 查看权限
  UPLOAD = 2, // 上传权限
  DELETE = 4, // 删除权限
  SHARE = 8, // 分享权限
  MANAGE = 16, // 管理权限（重命名、移动、设置权限等）
  ADMIN = 32, // 管理员权限（完全控制）
}

// 权限位标签映射
export const PermissionLabels: Record<PermissionBit, string> = {
  [PermissionBit.VIEW]: '查看',
  [PermissionBit.UPLOAD]: '上传',
  [PermissionBit.DELETE]: '删除',
  [PermissionBit.SHARE]: '分享',
  [PermissionBit.MANAGE]: '管理',
  [PermissionBit.ADMIN]: '管理员',
}

// 权限主体
export interface PermissionSubject {
  id: number
  type: SubjectType
  name: string
  displayName: string
  avatar?: string

  // 扩展信息
  email?: string
  department?: string
  role?: string
  status?: 'active' | 'inactive' | 'disabled'
}

// ACL 规则
export interface AclRule {
  id: number
  targetType: 'directory' | 'file'
  targetId: number
  subjectId: number
  permissionSet: number // 权限位掩码
  effect: RuleEffect
  priority: number
  createdBy: number
  createdAt: string
  updatedAt: string

  // 关联信息
  subject?: PermissionSubject
  target?: {
    id: number
    name: string
    type: 'directory' | 'file'
  }
  creator?: {
    id: number
    name: string
  }
  conditions?: AclCondition[]

  // 计算属性
  permissions?: PermissionBit[]
  isActive?: boolean
}

// ACL 条件
export interface AclCondition {
  id: number
  ruleId: number
  conditionType: ConditionType
  conditionValue: string
  createdAt: string

  // 关联信息
  rule?: AclRule

  // 解析后的值
  parsedValue?: any
  isValid?: boolean
}

// 创建 ACL 规则请求
export interface CreateAclRuleRequest {
  targetType: 'directory' | 'file'
  targetId: number
  subjectId: number
  permissionSet: number
  effect: RuleEffect
  priority?: number
  conditions?: Array<{
    conditionType: ConditionType
    conditionValue: string
  }>
}

// 添加条件请求
export interface AddConditionRequest {
  conditionType: ConditionType
  conditionValue: string
}

// 权限检查请求
export interface PermissionCheckRequest {
  userId: number
  targetType: 'directory' | 'file'
  targetId: number
  permissionBit: PermissionBit
  context?: Record<string, any>
}

// 权限检查响应
export interface PermissionCheckResponse {
  hasPermission: boolean
  effectivePermissions: number
  appliedRules: Array<{
    ruleId: number
    effect: RuleEffect
    permissions: number
    priority: number
  }>
  inheritedFrom?: {
    type: 'parent' | 'owner' | 'default'
    id?: number
    name?: string
  }
}

// 计算有效权限请求
export interface ComputePermissionsRequest {
  userId: number
  targetType: 'directory' | 'file'
  targetId: number
  includeInherited?: boolean
}

// 计算有效权限响应
export interface ComputePermissionsResponse {
  effectivePermissions: number
  permissions: PermissionBit[]
  rules: Array<{
    ruleId: number
    effect: RuleEffect
    permissions: number
    priority: number
    source: 'direct' | 'inherited'
    sourceId?: number
  }>
  inheritance?: {
    parentId?: number
    ownerId?: number
    defaultPermissions?: number
  }
}

// 权限列表查询参数
export interface PermissionListParams {
  targetType?: 'directory' | 'file'
  targetId?: number
  subjectId?: number
  subjectType?: SubjectType
  effect?: RuleEffect
  includeConditions?: boolean

  // 通用查询参数
  page?: number
  pageSize?: number
  sort?: string
  order?: 'ASC' | 'DESC'
}

// 权限列表响应
export interface PermissionListResponse {
  data: AclRule[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 主体列表查询参数
export interface SubjectListParams {
  type?: SubjectType
  status?: 'active' | 'inactive' | 'disabled'
  departmentId?: number
  roleId?: number

  // 通用查询参数
  page?: number
  pageSize?: number
  search?: string
  searchFields?: string[]
}

// 主体列表响应
export interface SubjectListResponse {
  data: PermissionSubject[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 权限模板
export interface PermissionTemplate {
  id: number
  name: string
  description: string
  permissions: number
  isDefault: boolean
  createdBy: number
  createdAt: string

  // 使用统计
  usageCount?: number
}

// 权限继承信息
export interface PermissionInheritance {
  targetId: number
  targetType: 'directory' | 'file'
  parentId?: number
  ownerId?: number
  inheritedPermissions: number
  directPermissions: number
  effectivePermissions: number

  // 继承路径
  inheritancePath?: Array<{
    id: number
    name: string
    type: 'directory' | 'file'
    permissions: number
  }>
}

// 权限变更历史
export interface PermissionHistory {
  id: number
  targetType: 'directory' | 'file'
  targetId: number
  action: 'grant' | 'revoke' | 'modify'
  subjectId: number
  oldPermissions: number
  newPermissions: number
  changedBy: number
  changedAt: string
  reason?: string

  // 关联信息
  subject?: PermissionSubject
  target?: {
    id: number
    name: string
    type: 'directory' | 'file'
  }
  changer?: {
    id: number
    name: string
  }
}

// 权限统计信息
export interface PermissionStats {
  totalRules: number
  activeRules: number
  subjectStats: Array<{
    type: SubjectType
    count: number
  }>
  permissionStats: Array<{
    permission: PermissionBit
    count: number
  }>
  recentChanges: PermissionHistory[]
}
