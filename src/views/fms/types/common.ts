/**
 * FMS 通用类型定义
 */

// 基础响应结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页响应结构
export interface PaginatedResponse<T = any> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  pageSize?: number
  sort?: string
  order?: 'ASC' | 'DESC'
}

// 筛选参数
export interface FilterParams {
  filter?: Record<string, any>
  op?: Record<string, string>
  search?: string
  searchFields?: string[]
}

// 查询参数（分页 + 筛选）
export interface QueryParams extends PaginationParams, FilterParams {}

// 表单字段配置
export interface FormField {
  prop: string
  label: string
  type: 'input' | 'select' | 'daterange' | 'datetime' | 'number' | 'textarea'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  multiple?: boolean
  required?: boolean
  rules?: any[]
}

// 面包屑项
export interface BreadcrumbItem {
  id?: number
  label: string
  to?: string
}

// 操作按钮配置
export interface ActionButton {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  permission?: number
  disabled?: boolean
  loading?: boolean
  onClick: () => void
}

// 表格列配置
export interface TableColumn {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any) => string
  render?: (row: any) => any
}

// 上传文件信息
export interface UploadFileInfo {
  name: string
  size: number
  type: string
  url?: string
  status?: 'ready' | 'uploading' | 'success' | 'error'
  percent?: number
}

// 错误信息
export interface ErrorInfo {
  code: string
  message: string
  details?: any
}

// 权限相关
export interface PermissionContext {
  targetType: 'directory' | 'file'
  targetId: number
  userId?: number
}

// 操作日志相关
export interface OperationContext {
  action: string
  targetType: 'directory' | 'file' | 'tag' | 'share' | 'acl'
  targetId: number
  detail?: Record<string, any>
}

// 通用状态枚举
export enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 可见性枚举
export enum Visibility {
  PUBLIC = 'public',
  PRIVATE = 'private',
  INTERNAL = 'internal',
}

// 排序方向
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

// 文件类型分类
export enum FileCategory {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  ARCHIVE = 'archive',
  CODE = 'code',
  OTHER = 'other',
}

// 操作类型
export enum ActionType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move',
  COPY = 'copy',
  RENAME = 'rename',
  UPLOAD = 'upload',
  DOWNLOAD = 'download',
  SHARE = 'share',
  RESTORE = 'restore',
  PURGE = 'purge',
}

// 主体类型（ACL）
export enum SubjectType {
  USER = 'user',
  DEPARTMENT = 'department',
  ROLE = 'role',
}

// 条件类型（ACL）
export enum ConditionType {
  TIME_RANGE = 'time_range',
  IP_RANGE = 'ip_range',
  FILE_SIZE = 'file_size',
  FILE_TYPE = 'file_type',
  CUSTOM = 'custom',
}

// 规则效果（ACL）
export enum RuleEffect {
  ALLOW = 'allow',
  DENY = 'deny',
}
